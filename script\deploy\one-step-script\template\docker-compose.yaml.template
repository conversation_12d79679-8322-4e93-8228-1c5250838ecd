version: '3'

services:
  mysql:
    image: registry.cn-shenzhen.aliyuncs.com/breeze-project/mysql:8.0.33
    container_name: ruoyi-ai-mysql
    env_file:
      - ./.env
    environment:
      - MYSQL_ROOT_PASSWORD={{MYSQL_ROOT_PASSWORD}}
      - MYSQL_DATABASE={{MYSQL_DATABASE}}
    ports:
      - "{{MYSQL_PORT}}:3306"
    volumes:
      - ./mysql-init:/docker-entrypoint-initdb.d
      - ./data/mysql:/var/lib/mysql
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
    restart: always
    networks:
      - ruoyi-net

  redis:
    image: registry.cn-shenzhen.aliyuncs.com/breeze-project/redis:6.2
    container_name: ruoyi-ai-redis
    env_file:
      - ./.env
    ports:
      - "{{REDIS_PORT}}:6379"
    volumes:
      - ./data/redis:/data
    command: redis-server --appendonly yes ${REDIS_PASSWORD:+--requirepass ${REDIS_PASSWORD}}
    restart: always
    networks:
      - ruoyi-net

  weaviate:
    image: registry.cn-shenzhen.aliyuncs.com/breeze-project/weaviate:1.30.0
    container_name: ruoyi-ai-weaviate
    ports:
      - "{{WEAVIATE_HTTP_PORT}}:8080"
      - "{{WEAVIATE_GRPC_PORT}}:50051"
    volumes:
      - ./data/weaviate:/var/lib/weaviate
    env_file:
      - ./.env
    environment:
      - QUERY_DEFAULTS_LIMIT={{WEAVIATE_QUERY_LIMIT}}
      - AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED={{WEAVIATE_ANONYMOUS_ACCESS}}
      - PERSISTENCE_DATA_PATH={{WEAVIATE_DATA_PATH}}
      - DEFAULT_VECTORIZER_MODULE={{WEAVIATE_VECTORIZER_MODULE}}
      - ENABLE_MODULES={{WEAVIATE_MODULES}}
      - CLUSTER_HOSTNAME={{WEAVIATE_CLUSTER_HOSTNAME}}
    command: --host 0.0.0.0 --port 8080 --scheme http
    restart: always
    networks:
      - ruoyi-net

  ruoyi-backend:
    image: ruoyi-ai-backend:latest
    container_name: ruoyi-ai-backend
    env_file:
      - ./.env
    ports:
      - "{{SERVER_PORT}}:{{SERVER_PORT}}"
    environment:
      - SERVER_PORT={{SERVER_PORT}}
      - DB_URL={{DB_URL}}
      - DB_USERNAME={{DB_USERNAME}}
      - DB_PASSWORD={{DB_PASSWORD}}
      - REDIS_HOST={{REDIS_HOST}}
      - REDIS_PORT={{REDIS_PORT}}
      - REDIS_DATABASE={{REDIS_DATABASE}}
      - REDIS_PASSWORD={{REDIS_PASSWORD}}
      - REDIS_TIMEOUT={{REDIS_TIMEOUT}}
      - TZ={{TZ}}
    volumes:
      - ./data/logs:/ruoyi/server/logs
    restart: always
    depends_on:
      - mysql
      - redis
      - weaviate
    networks:
      - ruoyi-net

  ruoyi-admin:
    image: ruoyi-ai-admin:latest
    container_name: ruoyi-ai-admin
    ports:
      - "{{ADMIN_PORT}}:80"
    restart: always
    depends_on:
      - ruoyi-backend
    networks:
      - ruoyi-net

  ruoyi-web:
    image: ruoyi-ai-web:latest
    container_name: ruoyi-ai-web
    ports:
      - "{{WEB_PORT}}:80"
    restart: always
    depends_on:
      - ruoyi-backend
    networks:
      - ruoyi-net

networks:
  ruoyi-net:
    driver: bridge
