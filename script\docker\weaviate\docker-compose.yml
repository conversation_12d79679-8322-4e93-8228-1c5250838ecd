---
services:
  weaviate:
    command:
      - --host
      - 0.0.0.0
      - --port
      - '6038'
      - --scheme
      - http
    image: semitechnologies/weaviate:1.19.7
    ports:
      - 6038:6038
      - 50051:50051
    volumes:
      - weaviate_data:/var/lib/weaviate
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      CLUSTER_HOSTNAME: 'node1'
volumes:
  weaviate_data:
...
