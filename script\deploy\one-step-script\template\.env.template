# Timezone
TZ={{TZ}}

# MySQL Configuration
MYSQL_ROOT_PASSWORD={{MYSQL_ROOT_PASSWORD}}
MYSQL_DATABASE={{MYSQL_DATABASE}}
MYSQL_PORT={{MYSQL_PORT}}

# Redis Configuration
REDIS_PORT={{REDIS_PORT}}
REDIS_PASSWORD={{REDIS_PASSWORD}}
REDIS_DATABASE={{REDIS_DATABASE}}
REDIS_TIMEOUT={{REDIS_TIMEOUT}}

# Backend Configuration
SERVER_PORT={{SERVER_PORT}}
DB_URL=***********************/{{MYSQL_DATABASE}}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&autoReconnect=true&rewriteBatchedStatements=true
DB_USERNAME={{DB_USERNAME}}
DB_PASSWORD={{DB_PASSWORD}}
REDIS_HOST=redis
BACKEND_HOST={{BACKEND_HOST}}

# Frontend Configuration
ADMIN_PORT={{ADMIN_PORT}}
WEB_PORT={{WEB_PORT}}
FRONTEND_API_BASE_URL={{FRONTEND_API_BASE_URL}}
FRONTEND_DEV_PORT={{FRONTEND_DEV_PORT}}

# Weaviate Configuration
WEAVIATE_HTTP_PORT={{WEAVIATE_HTTP_PORT}}
WEAVIATE_GRPC_PORT={{WEAVIATE_GRPC_PORT}}
WEAVIATE_QUERY_LIMIT={{WEAVIATE_QUERY_LIMIT}}
WEAVIATE_ANONYMOUS_ACCESS={{WEAVIATE_ANONYMOUS_ACCESS}}
WEAVIATE_DATA_PATH={{WEAVIATE_DATA_PATH}}
WEAVIATE_VECTORIZER_MODULE={{WEAVIATE_VECTORIZER_MODULE}}
WEAVIATE_MODULES={{WEAVIATE_MODULES}}
WEAVIATE_CLUSTER_HOSTNAME={{WEAVIATE_CLUSTER_HOSTNAME}}
WEAVIATE_PROTOCOL={{WEAVIATE_PROTOCOL}}
WEAVIATE_HOST=weaviate:8080
WEAVIATE_CLASSNAME={{WEAVIATE_CLASSNAME}}

# Production Configuration
PROD_DB_URL={{PROD_DB_URL}}
PROD_DB_USERNAME={{PROD_DB_USERNAME}}
PROD_DB_PASSWORD={{PROD_DB_PASSWORD}}
PROD_REDIS_HOST={{PROD_REDIS_HOST}}
PROD_REDIS_PORT={{PROD_REDIS_PORT}}
PROD_REDIS_DATABASE={{PROD_REDIS_DATABASE}}
PROD_REDIS_PASSWORD={{PROD_REDIS_PASSWORD}}
PROD_REDIS_TIMEOUT={{PROD_REDIS_TIMEOUT}}