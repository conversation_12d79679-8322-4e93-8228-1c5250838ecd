package org.ruoyi.chat.controller.chat;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.ruoyi.common.core.domain.R;
import org.ruoyi.common.core.validate.AddGroup;
import org.ruoyi.common.core.validate.EditGroup;
import org.ruoyi.common.excel.utils.ExcelUtil;
import org.ruoyi.common.idempotent.annotation.RepeatSubmit;
import org.ruoyi.common.log.annotation.Log;
import org.ruoyi.common.log.enums.BusinessType;
import org.ruoyi.common.web.core.BaseController;
import org.ruoyi.core.page.PageQuery;
import org.ruoyi.core.page.TableDataInfo;
import org.ruoyi.domain.bo.ChatMessageBo;
import org.ruoyi.domain.bo.ChatMessageForUniappBo;
import org.ruoyi.domain.vo.ChatMessageVo;
import org.ruoyi.service.IChatMessageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 聊天消息
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/message")
public class ChatMessageController extends BaseController {

    private final IChatMessageService chatMessageService;

    /**
     * 查询聊天消息列表
     */
    @GetMapping("/list")
    public TableDataInfo<ChatMessageVo> list(ChatMessageBo bo, PageQuery pageQuery) {
        return chatMessageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出聊天消息列表
     */
    @Log(title = "聊天消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ChatMessageBo bo, HttpServletResponse response) {
        List<ChatMessageVo> list = chatMessageService.queryList(bo);
        ExcelUtil.exportExcel(list, "聊天消息", ChatMessageVo.class, response);
    }

    /**
     * 获取聊天消息详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ChatMessageVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(chatMessageService.queryById(id));
    }



    /**
     * 查询聊天消息列表 uniapp
     */
    @GetMapping("/listForUniapp")
    public TableDataInfo<ChatMessageVo> list(ChatMessageForUniappBo uniappBo, PageQuery pageQuery) {
        ChatMessageBo bo = new ChatMessageBo();
        bo.setUserId(uniappBo.getUserId());
        bo.setSessionId(Long.parseLong(uniappBo.getUserId().toString() + "2024"));
        return chatMessageService.queryPageList(bo, pageQuery);
    }


    /**
     * 新增聊天消息 uniapp
     */
    @Log(title = "聊天消息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/addForUniapp")
    public R<Long> addForUniapp(@Validated(AddGroup.class) @RequestBody ChatMessageForUniappBo uniappBo) {
        ChatMessageBo bo = new ChatMessageBo();
        bo.setUserId(uniappBo.getUserId());
        bo.setRole(uniappBo.getRole());
        bo.setContent(uniappBo.getContent());
        bo.setSessionId(Long.parseLong(uniappBo.getUserId().toString() + "2024"));
        chatMessageService.insertByBo(bo);
        return R.ok(uniappBo.getId());
    }


    /**
     * 新增聊天消息
     */
    @Log(title = "聊天消息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody ChatMessageBo bo) {
        chatMessageService.insertByBo(bo);
        return R.ok(bo.getId());
    }

    /**
     * 修改聊天消息
     */
    @Log(title = "聊天消息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ChatMessageBo bo) {
        return toAjax(chatMessageService.updateByBo(bo));
    }

    /**
     * 删除聊天消息
     *
     * @param ids 主键串
     */
    @Log(title = "聊天消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(chatMessageService.deleteWithValidByIds(List.of(ids), true));
    }
}
