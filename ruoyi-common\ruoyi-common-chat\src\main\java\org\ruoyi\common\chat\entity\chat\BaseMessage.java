package org.ruoyi.common.chat.entity.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.ruoyi.common.chat.entity.chat.tool.ToolCalls;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 1.1.2
 * 2023-03-02
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
public class BaseMessage implements Serializable {

    /**
     * 目前支持四个中角色参考官网，进行情景输入：
     * https://platform.openai.com/docs/guides/chat/introduction
     */
    private String role;


    private String name;

    /**
     * The tool calls generated by the model, such as function calls.
     * @since 1.1.2
     */
    @JsonProperty("tool_calls")
    private List<ToolCalls> toolCalls;

    /**
     * @since 1.1.2
     */
    @JsonProperty("tool_call_id")
    private String toolCallId;

    @Deprecated
    @JsonProperty("function_call")
    private FunctionCall functionCall;


    /**
     * 构造函数
     *
     * @param role         角色
     * @param name         name
     * @param functionCall functionCall
     */
    public BaseMessage(String role, String name, FunctionCall functionCall) {
        this.role = role;
        this.name = name;
        this.functionCall = functionCall;
    }

    public BaseMessage() {
    }


    @Getter
    @AllArgsConstructor
    public enum Role {

        SYSTEM("system"),
        USER("user"),
        ASSISTANT("assistant"),
        FUNCTION("function"),
        TOOL("tool"),
        ;
        private final String name;
    }

}
